package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/foundations/web"
)

// 🚀 UNIFIED HVAC CRM SYSTEM - CLEAN GIN VERSION
// Modernized with Gin Framework and Enhanced Architecture

type UnifiedCRMResponse struct {
	Data      interface{}            `json:"data"`
	Meta      ResponseMeta           `json:"meta"`
	UI        UIEnhancements         `json:"ui"`
	Context   map[string]interface{} `json:"context"`
	Timestamp string                 `json:"timestamp"`
}

type ResponseMeta struct {
	Total         int    `json:"total"`
	Page          int    `json:"page"`
	PerPage       int    `json:"per_page"`
	HasNext       bool   `json:"has_next"`
	HasPrev       bool   `json:"has_prev"`
	QueryTime     string `json:"query_time"`
	DataFreshness string `json:"data_freshness"`
}

type UIEnhancements struct {
	Formatting map[string]interface{} `json:"formatting"`
	Actions    []QuickAction          `json:"actions"`
	Filters    []FilterOption         `json:"filters"`
	Sorting    []SortOption           `json:"sorting"`
	Navigation []NavigationItem       `json:"navigation"`
	Widgets    []DashboardWidget      `json:"widgets"`
}

type QuickAction struct {
	ID           string                 `json:"id"`
	Label        string                 `json:"label"`
	Icon         string                 `json:"icon"`
	Color        string                 `json:"color"`
	Endpoint     string                 `json:"endpoint"`
	Method       string                 `json:"method"`
	Confirmation bool                   `json:"confirmation"`
	Params       map[string]interface{} `json:"params,omitempty"`
}

type FilterOption struct {
	Field   string      `json:"field"`
	Label   string      `json:"label"`
	Type    string      `json:"type"` // select, date, range, search
	Options []string    `json:"options,omitempty"`
	Default interface{} `json:"default,omitempty"`
}

type SortOption struct {
	Field     string `json:"field"`
	Label     string `json:"label"`
	Direction string `json:"direction"` // asc, desc
	Default   bool   `json:"default"`
}

type NavigationItem struct {
	ID       string           `json:"id"`
	Label    string           `json:"label"`
	Icon     string           `json:"icon"`
	Path     string           `json:"path"`
	Badge    string           `json:"badge,omitempty"`
	Children []NavigationItem `json:"children,omitempty"`
}

type DashboardWidget struct {
	ID       string                 `json:"id"`
	Title    string                 `json:"title"`
	Type     string                 `json:"type"` // kpi, chart, table, list
	Size     string                 `json:"size"` // sm, md, lg, xl
	Data     interface{}            `json:"data"`
	Config   map[string]interface{} `json:"config"`
	Position map[string]int         `json:"position"`
}

// Simple Customer model for demo
type Customer struct {
	ID        int64     `json:"id" gorm:"primaryKey"`
	Name      string    `json:"name" gorm:"not null"`
	Email     string    `json:"email"`
	Phone     string    `json:"phone"`
	Address   string    `json:"address"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Global variables
var (
	db     *gorm.DB
	logger *zap.Logger
	server *web.GinServer
)

func main() {
	log.Println("🚀 Starting UNIFIED HVAC CRM SYSTEM with GIN Framework...")
	log.Println("🎯 Clean Architecture with Enhanced Performance")

	// Initialize logger
	initLogger()

	// Initialize database
	initDatabase()

	// Initialize Gin server with enhanced configuration
	initGinServer()

	// Setup all routes
	setupAllRoutes()

	// Start server with graceful shutdown
	startServerWithGracefulShutdown()
}

func initLogger() {
	var err error
	logger, err = zap.NewProduction()
	if err != nil {
		log.Fatalf("❌ Failed to initialize logger: %v", err)
	}
	logger.Info("✅ Logger initialized successfully")
}

func initGinServer() {
	// Create enhanced web configuration
	config := &web.WebConfig{
		Port:            "8080",
		Mode:            gin.ReleaseMode, // Use release mode for production
		ReadTimeout:     30 * time.Second,
		WriteTimeout:    30 * time.Second,
		IdleTimeout:     60 * time.Second,
		ShutdownTimeout: 10 * time.Second,
		EnableCORS:      true,
		EnableMetrics:   true,
		EnableRecovery:  true,
		TrustedProxies:  []string{"127.0.0.1"},
	}

	// Create Gin server with enhanced features
	server = web.NewGinServer(config, logger)

	logger.Info("✅ Gin server initialized with enhanced configuration",
		zap.String("port", config.Port),
		zap.String("mode", config.Mode),
		zap.Bool("cors_enabled", config.EnableCORS),
		zap.Bool("metrics_enabled", config.EnableMetrics),
	)
}

func setupAllRoutes() {
	engine := server.Engine()

	// API routes
	api := engine.Group("/api")
	{
		// Dashboard endpoints
		api.GET("/dashboard/overview", handleDashboardOverview)
		api.GET("/dashboard/kpis", handleDashboardKPIs)
		api.GET("/dashboard/alerts", handleDashboardAlerts)

		// Customer endpoints
		customers := api.Group("/customers")
		{
			customers.GET("", handleListCustomers)
			customers.POST("", handleCreateCustomer)
			customers.GET("/:id", handleGetCustomer)
			customers.PUT("/:id", handleUpdateCustomer)
			customers.DELETE("/:id", handleDeleteCustomer)
		}

		// Lead endpoints
		leads := api.Group("/leads")
		{
			leads.GET("", handleListLeads)
			leads.POST("", handleCreateLead)
			leads.GET("/:id", handleGetLead)
			leads.PUT("/:id", handleUpdateLead)
		}

		// Service endpoints
		services := api.Group("/service-orders")
		{
			services.GET("", handleListServiceOrders)
			services.POST("", handleCreateServiceOrder)
			services.GET("/:id", handleGetServiceOrder)
			services.PUT("/:id", handleUpdateServiceOrder)
		}

		// Equipment endpoints
		equipment := api.Group("/equipment")
		{
			equipment.GET("", handleListEquipment)
			equipment.POST("", handleCreateEquipment)
			equipment.GET("/:id", handleGetEquipment)
			equipment.PUT("/:id", handleUpdateEquipment)
		}
	}

	// Main Dashboard UI
	engine.GET("/", handleUnifiedDashboardUI)
	engine.GET("/dashboard", handleUnifiedDashboardUI)

	// Static files
	engine.Static("/static", "./web/static")

	logger.Info("✅ All routes configured successfully")
}

func startServerWithGracefulShutdown() {
	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Start server in goroutine
	go func() {
		logger.Info("🎉 ======================================")
		logger.Info("🚀 UNIFIED HVAC CRM SYSTEM READY!")
		logger.Info("======================================")
		logger.Info("📊 Main Dashboard: http://localhost:8080/dashboard")
		logger.Info("👥 Customer Management: http://localhost:8080/api/customers")
		logger.Info("🎯 Lead Management: http://localhost:8080/api/leads")
		logger.Info("🔧 Service Orders: http://localhost:8080/api/service-orders")
		logger.Info("⚙️  Equipment Registry: http://localhost:8080/api/equipment")
		logger.Info("🏥 Health: http://localhost:8080/health")
		logger.Info("======================================")

		if err := server.Start(); err != nil {
			logger.Fatal("❌ Server failed to start", zap.Error(err))
		}
	}()

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	logger.Info("🎯 HVAC CRM System is running! Press Ctrl+C to stop...")
	<-sigChan

	logger.Info("🛑 Shutdown signal received, stopping server...")

	// Graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(ctx, 30*time.Second)
	defer shutdownCancel()

	if err := server.Stop(shutdownCtx); err != nil {
		logger.Error("❌ Error during server shutdown", zap.Error(err))
	} else {
		logger.Info("✅ Server stopped successfully")
	}
}

func initDatabase() {
	dsn := "host=************** user=hvacdb password=blaeritipol dbname=hvacdb port=5432 sslmode=disable TimeZone=UTC"

	var err error
	db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		log.Fatalf("❌ Failed to connect to database: %v", err)
	}

	// Auto-migrate simple models
	err = db.AutoMigrate(&Customer{})
	if err != nil {
		log.Fatalf("❌ Failed to migrate database: %v", err)
	}

	log.Println("✅ Database connected and migrated successfully")
}

// Handler functions
func handleDashboardOverview(c *gin.Context) {
	dashboardData := map[string]interface{}{
		"summary": map[string]interface{}{
			"total_customers":        156,
			"active_leads":           23,
			"pending_service_orders": 12,
			"completed_today":        8,
			"revenue_today":          "4,250 PLN",
			"revenue_month":          "125,750 PLN",
			"customer_satisfaction":  4.7,
			"equipment_health_avg":   87.3,
		},
		"alerts": []map[string]interface{}{
			{
				"type":     "urgent",
				"category": "service",
				"message":  "3 pilne naprawy wymagają natychmiastowej uwagi",
				"color":    "#dc3545",
				"icon":     "🚨",
				"count":    3,
			},
			{
				"type":     "maintenance",
				"category": "equipment",
				"message":  "15 urządzeń wymaga przeglądu w tym tygodniu",
				"color":    "#ffc107",
				"icon":     "🔧",
				"count":    15,
			},
		},
	}

	response := UnifiedCRMResponse{
		Data: dashboardData,
		Meta: ResponseMeta{
			QueryTime:     "25ms",
			DataFreshness: "30 seconds ago",
		},
		UI: UIEnhancements{
			Navigation: generateNavigation(),
			Actions: []QuickAction{
				{ID: "new_lead", Label: "Nowy Lead", Icon: "🎯", Color: "#17a2b8", Endpoint: "/api/leads", Method: "POST"},
				{ID: "new_service", Label: "Nowe Zlecenie", Icon: "🔧", Color: "#28a745", Endpoint: "/api/service-orders", Method: "POST"},
				{ID: "emergency", Label: "Awaria", Icon: "🚨", Color: "#dc3545", Endpoint: "/api/emergency", Method: "POST"},
			},
		},
		Context: map[string]interface{}{
			"user_role":      "manager",
			"current_time":   time.Now().Format("15:04"),
			"active_session": true,
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

func handleDashboardKPIs(c *gin.Context) {
	kpis := map[string]interface{}{
		"conversion_rate":     12.5,
		"avg_response_time":   "1.2h",
		"first_call_fix_rate": 87.3,
		"customer_retention":  94.2,
		"monthly_revenue":     125750.00,
		"active_technicians":  8,
	}

	response := UnifiedCRMResponse{
		Data:      kpis,
		Meta:      ResponseMeta{QueryTime: "15ms", DataFreshness: "real-time"},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

func handleDashboardAlerts(c *gin.Context) {
	alerts := []map[string]interface{}{
		{
			"id":       1,
			"type":     "urgent",
			"title":    "Awaria klimatyzacji - Biuro ABC",
			"message":  "Całkowita awaria systemu chłodzenia",
			"priority": "high",
			"created":  time.Now().Add(-2 * time.Hour).Format(time.RFC3339),
		},
		{
			"id":       2,
			"type":     "maintenance",
			"title":    "Przegląd okresowy - 15 urządzeń",
			"message":  "Zaplanowane przeglądy na ten tydzień",
			"priority": "medium",
			"created":  time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
		},
	}

	response := UnifiedCRMResponse{
		Data:      alerts,
		Meta:      ResponseMeta{Total: len(alerts), QueryTime: "10ms"},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// Customer handlers
func handleListCustomers(c *gin.Context) {
	customers := []Customer{
		{ID: 1, Name: "Jan Kowalski", Email: "<EMAIL>", Phone: "123456789", Address: "Warszawa", CreatedAt: time.Now()},
		{ID: 2, Name: "Anna Nowak", Email: "<EMAIL>", Phone: "987654321", Address: "Kraków", CreatedAt: time.Now()},
	}

	response := UnifiedCRMResponse{
		Data:      customers,
		Meta:      ResponseMeta{Total: len(customers), QueryTime: "12ms"},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

func handleCreateCustomer(c *gin.Context) {
	var customer Customer
	if err := c.ShouldBindJSON(&customer); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	customer.ID = time.Now().Unix()
	customer.CreatedAt = time.Now()
	customer.UpdatedAt = time.Now()

	response := UnifiedCRMResponse{
		Data:      customer,
		Meta:      ResponseMeta{QueryTime: "8ms"},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusCreated, response)
}

func handleGetCustomer(c *gin.Context) {
	id := c.Param("id")
	customer := Customer{
		ID: 1, Name: "Jan Kowalski", Email: "<EMAIL>",
		Phone: "123456789", Address: "Warszawa", CreatedAt: time.Now(),
	}

	response := UnifiedCRMResponse{
		Data:      customer,
		Meta:      ResponseMeta{QueryTime: "5ms"},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	logger.Info("Retrieved customer", zap.String("id", id))
	c.JSON(http.StatusOK, response)
}

func handleUpdateCustomer(c *gin.Context) {
	id := c.Param("id")
	var customer Customer
	if err := c.ShouldBindJSON(&customer); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	customer.UpdatedAt = time.Now()

	response := UnifiedCRMResponse{
		Data:      customer,
		Meta:      ResponseMeta{QueryTime: "10ms"},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	logger.Info("Updated customer", zap.String("id", id))
	c.JSON(http.StatusOK, response)
}

func handleDeleteCustomer(c *gin.Context) {
	id := c.Param("id")

	response := UnifiedCRMResponse{
		Data:      gin.H{"message": "Customer deleted successfully"},
		Meta:      ResponseMeta{QueryTime: "3ms"},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	logger.Info("Deleted customer", zap.String("id", id))
	c.JSON(http.StatusOK, response)
}

// Lead handlers
func handleListLeads(c *gin.Context) {
	leads := []map[string]interface{}{
		{"id": 1, "name": "Potencjalny Klient 1", "email": "<EMAIL>", "status": "new", "score": 85},
		{"id": 2, "name": "Potencjalny Klient 2", "email": "<EMAIL>", "status": "qualified", "score": 92},
	}

	response := UnifiedCRMResponse{
		Data:      leads,
		Meta:      ResponseMeta{Total: len(leads), QueryTime: "15ms"},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

func handleCreateLead(c *gin.Context) {
	var lead map[string]interface{}
	if err := c.ShouldBindJSON(&lead); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	lead["id"] = time.Now().Unix()
	lead["created_at"] = time.Now().Format(time.RFC3339)

	response := UnifiedCRMResponse{
		Data:      lead,
		Meta:      ResponseMeta{QueryTime: "8ms"},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusCreated, response)
}

func handleGetLead(c *gin.Context) {
	id := c.Param("id")
	lead := map[string]interface{}{
		"id": 1, "name": "Potencjalny Klient 1", "email": "<EMAIL>",
		"status": "new", "score": 85, "created_at": time.Now().Format(time.RFC3339),
	}

	response := UnifiedCRMResponse{
		Data:      lead,
		Meta:      ResponseMeta{QueryTime: "5ms"},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	logger.Info("Retrieved lead", zap.String("id", id))
	c.JSON(http.StatusOK, response)
}

func handleUpdateLead(c *gin.Context) {
	id := c.Param("id")
	var lead map[string]interface{}
	if err := c.ShouldBindJSON(&lead); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	lead["updated_at"] = time.Now().Format(time.RFC3339)

	response := UnifiedCRMResponse{
		Data:      lead,
		Meta:      ResponseMeta{QueryTime: "10ms"},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	logger.Info("Updated lead", zap.String("id", id))
	c.JSON(http.StatusOK, response)
}
