# dario.cat/mergo v1.0.0
## explicit; go 1.13
dario.cat/mergo
# github.com/Masterminds/goutils v1.1.1
## explicit
github.com/Masterminds/goutils
# github.com/Masterminds/semver/v3 v3.2.0
## explicit; go 1.18
github.com/Masterminds/semver/v3
# github.com/Masterminds/sprig/v3 v3.2.3
## explicit; go 1.13
github.com/Masterminds/sprig/v3
# github.com/bahlo/generic-list-go v0.2.0
## explicit; go 1.18
github.com/bahlo/generic-list-go
# github.com/beorn7/perks v1.0.1
## explicit; go 1.11
github.com/beorn7/perks/quantile
# github.com/buger/jsonparser v1.1.1
## explicit; go 1.13
github.com/buger/jsonparser
# github.com/bytedance/sonic v1.13.2
## explicit; go 1.17
github.com/bytedance/sonic
github.com/bytedance/sonic/ast
github.com/bytedance/sonic/decoder
github.com/bytedance/sonic/encoder
github.com/bytedance/sonic/internal/caching
github.com/bytedance/sonic/internal/compat
github.com/bytedance/sonic/internal/cpu
github.com/bytedance/sonic/internal/decoder/api
github.com/bytedance/sonic/internal/decoder/consts
github.com/bytedance/sonic/internal/decoder/errors
github.com/bytedance/sonic/internal/decoder/jitdec
github.com/bytedance/sonic/internal/decoder/optdec
github.com/bytedance/sonic/internal/encoder
github.com/bytedance/sonic/internal/encoder/alg
github.com/bytedance/sonic/internal/encoder/ir
github.com/bytedance/sonic/internal/encoder/vars
github.com/bytedance/sonic/internal/encoder/vm
github.com/bytedance/sonic/internal/encoder/x86
github.com/bytedance/sonic/internal/envs
github.com/bytedance/sonic/internal/jit
github.com/bytedance/sonic/internal/native
github.com/bytedance/sonic/internal/native/avx2
github.com/bytedance/sonic/internal/native/neon
github.com/bytedance/sonic/internal/native/sse
github.com/bytedance/sonic/internal/native/types
github.com/bytedance/sonic/internal/optcaching
github.com/bytedance/sonic/internal/resolver
github.com/bytedance/sonic/internal/rt
github.com/bytedance/sonic/internal/utils
github.com/bytedance/sonic/option
github.com/bytedance/sonic/unquote
github.com/bytedance/sonic/utf8
# github.com/bytedance/sonic/loader v0.2.4
## explicit; go 1.16
github.com/bytedance/sonic/loader
github.com/bytedance/sonic/loader/internal/abi
github.com/bytedance/sonic/loader/internal/iasm/expr
github.com/bytedance/sonic/loader/internal/iasm/x86_64
github.com/bytedance/sonic/loader/internal/rt
# github.com/cespare/xxhash/v2 v2.3.0
## explicit; go 1.11
github.com/cespare/xxhash/v2
# github.com/cloudwego/base64x v0.1.5
## explicit; go 1.16
github.com/cloudwego/base64x
github.com/cloudwego/base64x/internal/native
github.com/cloudwego/base64x/internal/native/avx2
github.com/cloudwego/base64x/internal/native/sse
github.com/cloudwego/base64x/internal/rt
# github.com/davecgh/go-spew v1.1.1
## explicit
github.com/davecgh/go-spew/spew
# github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f
## explicit
github.com/dgryski/go-rendezvous
# github.com/disintegration/imaging v1.6.2
## explicit
github.com/disintegration/imaging
# github.com/dlclark/regexp2 v1.10.0
## explicit; go 1.13
github.com/dlclark/regexp2
github.com/dlclark/regexp2/syntax
# github.com/dustin/go-humanize v1.0.1
## explicit; go 1.16
github.com/dustin/go-humanize
# github.com/emersion/go-imap v1.2.1
## explicit; go 1.13
github.com/emersion/go-imap
github.com/emersion/go-imap/client
github.com/emersion/go-imap/commands
github.com/emersion/go-imap/responses
github.com/emersion/go-imap/utf7
# github.com/emersion/go-sasl v0.0.0-20200509203442-7bfe0ed36a21
## explicit; go 1.12
github.com/emersion/go-sasl
# github.com/facebookgo/clock v0.0.0-20150410010913-600d898af40a
## explicit
github.com/facebookgo/clock
# github.com/fsnotify/fsnotify v1.6.0
## explicit; go 1.16
github.com/fsnotify/fsnotify
# github.com/gabriel-vasile/mimetype v1.4.8
## explicit; go 1.20
github.com/gabriel-vasile/mimetype
github.com/gabriel-vasile/mimetype/internal/charset
github.com/gabriel-vasile/mimetype/internal/json
github.com/gabriel-vasile/mimetype/internal/magic
# github.com/gin-contrib/cors v1.7.5
## explicit; go 1.23.0
github.com/gin-contrib/cors
# github.com/gin-contrib/sse v1.0.0
## explicit; go 1.13
github.com/gin-contrib/sse
# github.com/gin-contrib/zap v1.1.5
## explicit; go 1.23.0
github.com/gin-contrib/zap
# github.com/gin-gonic/gin v1.10.1
## explicit; go 1.20
github.com/gin-gonic/gin
github.com/gin-gonic/gin/binding
github.com/gin-gonic/gin/internal/bytesconv
github.com/gin-gonic/gin/internal/json
github.com/gin-gonic/gin/render
# github.com/go-ini/ini v1.67.0
## explicit
github.com/go-ini/ini
# github.com/go-kratos/aegis v0.2.0
## explicit; go 1.18
github.com/go-kratos/aegis/internal/consistent
github.com/go-kratos/aegis/internal/cpu
github.com/go-kratos/aegis/internal/window
github.com/go-kratos/aegis/ratelimit
github.com/go-kratos/aegis/ratelimit/bbr
github.com/go-kratos/aegis/subset
# github.com/go-kratos/kratos/v2 v2.8.4
## explicit; go 1.21
github.com/go-kratos/kratos/v2
github.com/go-kratos/kratos/v2/api/metadata
github.com/go-kratos/kratos/v2/config
github.com/go-kratos/kratos/v2/config/file
github.com/go-kratos/kratos/v2/encoding
github.com/go-kratos/kratos/v2/encoding/form
github.com/go-kratos/kratos/v2/encoding/json
github.com/go-kratos/kratos/v2/encoding/proto
github.com/go-kratos/kratos/v2/encoding/xml
github.com/go-kratos/kratos/v2/encoding/yaml
github.com/go-kratos/kratos/v2/errors
github.com/go-kratos/kratos/v2/internal/context
github.com/go-kratos/kratos/v2/internal/endpoint
github.com/go-kratos/kratos/v2/internal/host
github.com/go-kratos/kratos/v2/internal/httputil
github.com/go-kratos/kratos/v2/internal/matcher
github.com/go-kratos/kratos/v2/log
github.com/go-kratos/kratos/v2/metadata
github.com/go-kratos/kratos/v2/middleware
github.com/go-kratos/kratos/v2/middleware/logging
github.com/go-kratos/kratos/v2/middleware/metrics
github.com/go-kratos/kratos/v2/middleware/ratelimit
github.com/go-kratos/kratos/v2/middleware/recovery
github.com/go-kratos/kratos/v2/middleware/tracing
github.com/go-kratos/kratos/v2/registry
github.com/go-kratos/kratos/v2/selector
github.com/go-kratos/kratos/v2/selector/node/direct
github.com/go-kratos/kratos/v2/selector/wrr
github.com/go-kratos/kratos/v2/transport
github.com/go-kratos/kratos/v2/transport/grpc
github.com/go-kratos/kratos/v2/transport/grpc/resolver/direct
github.com/go-kratos/kratos/v2/transport/grpc/resolver/discovery
github.com/go-kratos/kratos/v2/transport/http
github.com/go-kratos/kratos/v2/transport/http/binding
github.com/go-kratos/kratos/v2/transport/http/status
# github.com/go-logr/logr v1.4.1
## explicit; go 1.18
github.com/go-logr/logr
github.com/go-logr/logr/funcr
# github.com/go-logr/stdr v1.2.2
## explicit; go 1.16
github.com/go-logr/stdr
# github.com/go-ole/go-ole v1.2.6
## explicit; go 1.12
github.com/go-ole/go-ole
github.com/go-ole/go-ole/oleutil
# github.com/go-playground/form/v4 v4.2.0
## explicit; go 1.13
github.com/go-playground/form/v4
# github.com/go-playground/locales v0.14.1
## explicit; go 1.17
github.com/go-playground/locales
github.com/go-playground/locales/currency
# github.com/go-playground/universal-translator v0.18.1
## explicit; go 1.18
github.com/go-playground/universal-translator
# github.com/go-playground/validator/v10 v10.26.0
## explicit; go 1.20
github.com/go-playground/validator/v10
# github.com/go-redis/redis/v8 v8.11.5
## explicit; go 1.17
github.com/go-redis/redis/v8
github.com/go-redis/redis/v8/internal
github.com/go-redis/redis/v8/internal/hashtag
github.com/go-redis/redis/v8/internal/hscan
github.com/go-redis/redis/v8/internal/pool
github.com/go-redis/redis/v8/internal/proto
github.com/go-redis/redis/v8/internal/rand
github.com/go-redis/redis/v8/internal/util
# github.com/goccy/go-json v0.10.5
## explicit; go 1.19
github.com/goccy/go-json
github.com/goccy/go-json/internal/decoder
github.com/goccy/go-json/internal/encoder
github.com/goccy/go-json/internal/encoder/vm
github.com/goccy/go-json/internal/encoder/vm_color
github.com/goccy/go-json/internal/encoder/vm_color_indent
github.com/goccy/go-json/internal/encoder/vm_indent
github.com/goccy/go-json/internal/errors
github.com/goccy/go-json/internal/runtime
# github.com/gogo/protobuf v1.3.2
## explicit; go 1.15
github.com/gogo/protobuf/jsonpb
github.com/gogo/protobuf/proto
github.com/gogo/protobuf/sortkeys
github.com/gogo/protobuf/types
# github.com/golang-jwt/jwt/v5 v5.1.0
## explicit; go 1.18
github.com/golang-jwt/jwt/v5
# github.com/golang/mock v1.6.0
## explicit; go 1.11
github.com/golang/mock/gomock
# github.com/google/uuid v1.6.0
## explicit
github.com/google/uuid
# github.com/google/wire v0.6.0
## explicit; go 1.12
github.com/google/wire
# github.com/goph/emperror v0.17.2
## explicit; go 1.12
github.com/goph/emperror
# github.com/gorilla/mux v1.8.1
## explicit; go 1.20
github.com/gorilla/mux
# github.com/gorilla/websocket v1.5.3
## explicit; go 1.12
github.com/gorilla/websocket
# github.com/grpc-ecosystem/go-grpc-middleware v1.4.0
## explicit; go 1.14
github.com/grpc-ecosystem/go-grpc-middleware/retry
github.com/grpc-ecosystem/go-grpc-middleware/util/backoffutils
github.com/grpc-ecosystem/go-grpc-middleware/util/metautils
# github.com/grpc-ecosystem/grpc-gateway/v2 v2.22.0
## explicit; go 1.21
github.com/grpc-ecosystem/grpc-gateway/v2/internal/httprule
github.com/grpc-ecosystem/grpc-gateway/v2/runtime
github.com/grpc-ecosystem/grpc-gateway/v2/utilities
# github.com/h2non/filetype v1.1.3
## explicit; go 1.13
github.com/h2non/filetype
github.com/h2non/filetype/matchers
github.com/h2non/filetype/matchers/isobmff
github.com/h2non/filetype/types
# github.com/hibiken/asynq v0.25.1
## explicit; go 1.22
github.com/hibiken/asynq
github.com/hibiken/asynq/internal/base
github.com/hibiken/asynq/internal/context
github.com/hibiken/asynq/internal/errors
github.com/hibiken/asynq/internal/log
github.com/hibiken/asynq/internal/proto
github.com/hibiken/asynq/internal/rdb
github.com/hibiken/asynq/internal/timeutil
# github.com/huandu/xstrings v1.3.3
## explicit; go 1.12
github.com/huandu/xstrings
# github.com/imdario/mergo v0.3.13
## explicit; go 1.13
github.com/imdario/mergo
# github.com/invopop/jsonschema v0.12.0
## explicit; go 1.18
github.com/invopop/jsonschema
# github.com/jackc/pgpassfile v1.0.0
## explicit; go 1.12
github.com/jackc/pgpassfile
# github.com/jackc/pgservicefile v0.0.0-20240606120523-5a60cdf6a761
## explicit; go 1.14
github.com/jackc/pgservicefile
# github.com/jackc/pgx/v5 v5.6.0
## explicit; go 1.20
github.com/jackc/pgx/v5
github.com/jackc/pgx/v5/internal/iobufpool
github.com/jackc/pgx/v5/internal/pgio
github.com/jackc/pgx/v5/internal/sanitize
github.com/jackc/pgx/v5/internal/stmtcache
github.com/jackc/pgx/v5/pgconn
github.com/jackc/pgx/v5/pgconn/ctxwatch
github.com/jackc/pgx/v5/pgconn/internal/bgreader
github.com/jackc/pgx/v5/pgproto3
github.com/jackc/pgx/v5/pgtype
github.com/jackc/pgx/v5/pgxpool
github.com/jackc/pgx/v5/stdlib
# github.com/jackc/puddle/v2 v2.2.2
## explicit; go 1.19
github.com/jackc/puddle/v2
github.com/jackc/puddle/v2/internal/genstack
# github.com/jinzhu/inflection v1.0.0
## explicit
github.com/jinzhu/inflection
# github.com/jinzhu/now v1.1.5
## explicit; go 1.12
github.com/jinzhu/now
# github.com/json-iterator/go v1.1.12
## explicit; go 1.12
github.com/json-iterator/go
# github.com/jung-kurt/gofpdf v1.16.2
## explicit; go 1.12
github.com/jung-kurt/gofpdf
# github.com/klauspost/compress v1.18.0
## explicit; go 1.22
github.com/klauspost/compress/internal/le
github.com/klauspost/compress/internal/race
github.com/klauspost/compress/s2
# github.com/klauspost/cpuid/v2 v2.2.10
## explicit; go 1.22
github.com/klauspost/cpuid/v2
# github.com/leodido/go-urn v1.4.0
## explicit; go 1.18
github.com/leodido/go-urn
github.com/leodido/go-urn/scim/schema
# github.com/lib/pq v1.10.9
## explicit; go 1.13
github.com/lib/pq
github.com/lib/pq/oid
github.com/lib/pq/scram
# github.com/lufia/plan9stats v0.0.0-20230326075908-cb1d2100619a
## explicit; go 1.16
github.com/lufia/plan9stats
# github.com/mailru/easyjson v0.7.7
## explicit; go 1.12
github.com/mailru/easyjson/buffer
github.com/mailru/easyjson/jwriter
# github.com/mattn/go-isatty v0.0.20
## explicit; go 1.15
github.com/mattn/go-isatty
# github.com/metoro-io/mcp-golang v0.13.0
## explicit; go 1.21
github.com/metoro-io/mcp-golang
github.com/metoro-io/mcp-golang/internal/datastructures
github.com/metoro-io/mcp-golang/internal/protocol
github.com/metoro-io/mcp-golang/transport
github.com/metoro-io/mcp-golang/transport/stdio
github.com/metoro-io/mcp-golang/transport/stdio/internal/stdio
# github.com/minio/crc64nvme v1.0.1
## explicit; go 1.22
github.com/minio/crc64nvme
# github.com/minio/md5-simd v1.1.2
## explicit; go 1.14
github.com/minio/md5-simd
# github.com/minio/minio-go/v7 v7.0.92
## explicit; go 1.23.0
github.com/minio/minio-go/v7
github.com/minio/minio-go/v7/internal/json
github.com/minio/minio-go/v7/pkg/cors
github.com/minio/minio-go/v7/pkg/credentials
github.com/minio/minio-go/v7/pkg/encrypt
github.com/minio/minio-go/v7/pkg/kvcache
github.com/minio/minio-go/v7/pkg/lifecycle
github.com/minio/minio-go/v7/pkg/notification
github.com/minio/minio-go/v7/pkg/replication
github.com/minio/minio-go/v7/pkg/s3utils
github.com/minio/minio-go/v7/pkg/set
github.com/minio/minio-go/v7/pkg/signer
github.com/minio/minio-go/v7/pkg/singleflight
github.com/minio/minio-go/v7/pkg/sse
github.com/minio/minio-go/v7/pkg/tags
# github.com/mitchellh/copystructure v1.0.0
## explicit
github.com/mitchellh/copystructure
# github.com/mitchellh/reflectwalk v1.0.0
## explicit
github.com/mitchellh/reflectwalk
# github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
## explicit
github.com/modern-go/concurrent
# github.com/modern-go/reflect2 v1.0.2
## explicit; go 1.12
github.com/modern-go/reflect2
# github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822
## explicit
github.com/munnerz/goautoneg
# github.com/nexus-rpc/sdk-go v0.3.0
## explicit; go 1.21
github.com/nexus-rpc/sdk-go/nexus
# github.com/nikolalohinski/gonja v1.5.3
## explicit; go 1.20
github.com/nikolalohinski/gonja
github.com/nikolalohinski/gonja/builtins
github.com/nikolalohinski/gonja/builtins/statements
github.com/nikolalohinski/gonja/config
github.com/nikolalohinski/gonja/docs
github.com/nikolalohinski/gonja/exec
github.com/nikolalohinski/gonja/loaders
github.com/nikolalohinski/gonja/nodes
github.com/nikolalohinski/gonja/parser
github.com/nikolalohinski/gonja/tokens
github.com/nikolalohinski/gonja/utils
# github.com/pelletier/go-toml/v2 v2.2.3
## explicit; go 1.21.0
github.com/pelletier/go-toml/v2
github.com/pelletier/go-toml/v2/internal/characters
github.com/pelletier/go-toml/v2/internal/danger
github.com/pelletier/go-toml/v2/internal/tracker
github.com/pelletier/go-toml/v2/unstable
# github.com/philhofer/fwd v1.1.3-0.20240916144458-20a13a1f6b7c
## explicit; go 1.20
github.com/philhofer/fwd
# github.com/philippgille/chromem-go v0.7.0
## explicit; go 1.21
github.com/philippgille/chromem-go
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/pkoukk/tiktoken-go v0.1.6
## explicit; go 1.19
github.com/pkoukk/tiktoken-go
# github.com/pmezard/go-difflib v1.0.0
## explicit
github.com/pmezard/go-difflib/difflib
# github.com/power-devops/perfstat v0.0.0-20221212215047-62379fc7944b
## explicit; go 1.14
github.com/power-devops/perfstat
# github.com/prometheus/client_golang v1.22.0
## explicit; go 1.22
github.com/prometheus/client_golang/prometheus
github.com/prometheus/client_golang/prometheus/internal
github.com/prometheus/client_golang/prometheus/promauto
# github.com/prometheus/client_model v0.6.1
## explicit; go 1.19
github.com/prometheus/client_model/go
# github.com/prometheus/common v0.62.0
## explicit; go 1.21
github.com/prometheus/common/expfmt
github.com/prometheus/common/model
# github.com/prometheus/procfs v0.15.1
## explicit; go 1.20
github.com/prometheus/procfs
github.com/prometheus/procfs/internal/fs
github.com/prometheus/procfs/internal/util
# github.com/redis/go-redis/v9 v9.7.0
## explicit; go 1.18
github.com/redis/go-redis/v9
github.com/redis/go-redis/v9/internal
github.com/redis/go-redis/v9/internal/hashtag
github.com/redis/go-redis/v9/internal/hscan
github.com/redis/go-redis/v9/internal/pool
github.com/redis/go-redis/v9/internal/proto
github.com/redis/go-redis/v9/internal/rand
github.com/redis/go-redis/v9/internal/util
# github.com/robfig/cron v1.2.0
## explicit
github.com/robfig/cron
# github.com/robfig/cron/v3 v3.0.1
## explicit; go 1.12
github.com/robfig/cron/v3
# github.com/rs/cors v1.11.1
## explicit; go 1.13
github.com/rs/cors
github.com/rs/cors/internal
# github.com/rs/xid v1.6.0
## explicit; go 1.16
github.com/rs/xid
# github.com/shirou/gopsutil/v3 v3.23.12
## explicit; go 1.15
github.com/shirou/gopsutil/v3/common
github.com/shirou/gopsutil/v3/cpu
github.com/shirou/gopsutil/v3/internal/common
# github.com/shoenig/go-m1cpu v0.1.6
## explicit; go 1.20
github.com/shoenig/go-m1cpu
# github.com/shopspring/decimal v1.2.0
## explicit; go 1.13
github.com/shopspring/decimal
# github.com/sirupsen/logrus v1.9.3
## explicit; go 1.13
github.com/sirupsen/logrus
# github.com/sony/gobreaker v1.0.0
## explicit; go 1.12
github.com/sony/gobreaker
# github.com/spf13/cast v1.7.0
## explicit; go 1.19
github.com/spf13/cast
# github.com/stretchr/objx v0.5.2
## explicit; go 1.20
github.com/stretchr/objx
# github.com/stretchr/testify v1.10.0
## explicit; go 1.17
github.com/stretchr/testify/assert
github.com/stretchr/testify/assert/yaml
github.com/stretchr/testify/mock
github.com/stretchr/testify/require
# github.com/tidwall/gjson v1.18.0
## explicit; go 1.12
github.com/tidwall/gjson
# github.com/tidwall/match v1.1.1
## explicit; go 1.15
github.com/tidwall/match
# github.com/tidwall/pretty v1.2.1
## explicit; go 1.16
github.com/tidwall/pretty
# github.com/tidwall/sjson v1.2.5
## explicit; go 1.14
github.com/tidwall/sjson
# github.com/tinylib/msgp v1.3.0
## explicit; go 1.20
github.com/tinylib/msgp/msgp
# github.com/tklauser/go-sysconf v0.3.12
## explicit; go 1.13
github.com/tklauser/go-sysconf
# github.com/tklauser/numcpus v0.6.1
## explicit; go 1.13
github.com/tklauser/numcpus
# github.com/tmc/langchaingo v0.1.13
## explicit; go 1.22.0
github.com/tmc/langchaingo/callbacks
github.com/tmc/langchaingo/chains
github.com/tmc/langchaingo/embeddings
github.com/tmc/langchaingo/internal/maputil
github.com/tmc/langchaingo/internal/setutil
github.com/tmc/langchaingo/internal/sliceutil
github.com/tmc/langchaingo/llms
github.com/tmc/langchaingo/llms/ollama
github.com/tmc/langchaingo/llms/ollama/internal/ollamaclient
github.com/tmc/langchaingo/llms/openai
github.com/tmc/langchaingo/llms/openai/internal/openaiclient
github.com/tmc/langchaingo/memory
github.com/tmc/langchaingo/outputparser
github.com/tmc/langchaingo/prompts
github.com/tmc/langchaingo/prompts/internal/fstring
github.com/tmc/langchaingo/schema
github.com/tmc/langchaingo/tools/sqldatabase
# github.com/twitchyliquid64/golang-asm v0.15.1
## explicit; go 1.13
github.com/twitchyliquid64/golang-asm/asm/arch
github.com/twitchyliquid64/golang-asm/bio
github.com/twitchyliquid64/golang-asm/dwarf
github.com/twitchyliquid64/golang-asm/goobj
github.com/twitchyliquid64/golang-asm/obj
github.com/twitchyliquid64/golang-asm/obj/arm
github.com/twitchyliquid64/golang-asm/obj/arm64
github.com/twitchyliquid64/golang-asm/obj/mips
github.com/twitchyliquid64/golang-asm/obj/ppc64
github.com/twitchyliquid64/golang-asm/obj/riscv
github.com/twitchyliquid64/golang-asm/obj/s390x
github.com/twitchyliquid64/golang-asm/obj/wasm
github.com/twitchyliquid64/golang-asm/obj/x86
github.com/twitchyliquid64/golang-asm/objabi
github.com/twitchyliquid64/golang-asm/src
github.com/twitchyliquid64/golang-asm/sys
github.com/twitchyliquid64/golang-asm/unsafeheader
# github.com/ugorji/go/codec v1.2.12
## explicit; go 1.11
github.com/ugorji/go/codec
# github.com/unidoc/freetype v0.2.3
## explicit; go 1.14
github.com/unidoc/freetype
github.com/unidoc/freetype/raster
github.com/unidoc/freetype/truetype
# github.com/unidoc/pkcs7 v0.2.0
## explicit; go 1.11
github.com/unidoc/pkcs7
# github.com/unidoc/timestamp v0.0.0-20200412005513-91597fd3793a
## explicit; go 1.11
github.com/unidoc/timestamp
# github.com/unidoc/unipdf/v3 v3.69.0
## explicit; go 1.20
github.com/unidoc/unipdf/v3/common
github.com/unidoc/unipdf/v3/common/license
github.com/unidoc/unipdf/v3/contentstream
github.com/unidoc/unipdf/v3/core
github.com/unidoc/unipdf/v3/core/security
github.com/unidoc/unipdf/v3/core/security/crypt
github.com/unidoc/unipdf/v3/extractor
github.com/unidoc/unipdf/v3/internal/bitwise
github.com/unidoc/unipdf/v3/internal/ccittfax
github.com/unidoc/unipdf/v3/internal/cmap
github.com/unidoc/unipdf/v3/internal/cmap/bcmaps
github.com/unidoc/unipdf/v3/internal/imageutil
github.com/unidoc/unipdf/v3/internal/jbig2
github.com/unidoc/unipdf/v3/internal/jbig2/basic
github.com/unidoc/unipdf/v3/internal/jbig2/bitmap
github.com/unidoc/unipdf/v3/internal/jbig2/decoder
github.com/unidoc/unipdf/v3/internal/jbig2/decoder/arithmetic
github.com/unidoc/unipdf/v3/internal/jbig2/decoder/huffman
github.com/unidoc/unipdf/v3/internal/jbig2/decoder/mmr
github.com/unidoc/unipdf/v3/internal/jbig2/document
github.com/unidoc/unipdf/v3/internal/jbig2/document/segments
github.com/unidoc/unipdf/v3/internal/jbig2/encoder/arithmetic
github.com/unidoc/unipdf/v3/internal/jbig2/encoder/classer
github.com/unidoc/unipdf/v3/internal/jbig2/errors
github.com/unidoc/unipdf/v3/internal/jbig2/internal
github.com/unidoc/unipdf/v3/internal/license
github.com/unidoc/unipdf/v3/internal/precision
github.com/unidoc/unipdf/v3/internal/sampling
github.com/unidoc/unipdf/v3/internal/strutils
github.com/unidoc/unipdf/v3/internal/textencoding
github.com/unidoc/unipdf/v3/internal/textencoding/internal/syncmap
github.com/unidoc/unipdf/v3/internal/timeutils
github.com/unidoc/unipdf/v3/internal/transform
github.com/unidoc/unipdf/v3/internal/uuid
github.com/unidoc/unipdf/v3/model
github.com/unidoc/unipdf/v3/model/internal/docutil
github.com/unidoc/unipdf/v3/model/internal/fonts
github.com/unidoc/unipdf/v3/model/mdp
github.com/unidoc/unipdf/v3/model/sigutil
github.com/unidoc/unipdf/v3/ps
# github.com/unidoc/unitype v0.5.1
## explicit; go 1.18
github.com/unidoc/unitype
github.com/unidoc/unitype/internal/strutils
# github.com/wk8/go-ordered-map/v2 v2.1.8
## explicit; go 1.18
github.com/wk8/go-ordered-map/v2
# github.com/yargevad/filepathx v1.0.0
## explicit; go 1.14
github.com/yargevad/filepathx
# github.com/yusufpapurcu/wmi v1.2.3
## explicit; go 1.16
github.com/yusufpapurcu/wmi
# go.opentelemetry.io/otel v1.26.0
## explicit; go 1.21
go.opentelemetry.io/otel
go.opentelemetry.io/otel/attribute
go.opentelemetry.io/otel/baggage
go.opentelemetry.io/otel/codes
go.opentelemetry.io/otel/internal
go.opentelemetry.io/otel/internal/attribute
go.opentelemetry.io/otel/internal/baggage
go.opentelemetry.io/otel/internal/global
go.opentelemetry.io/otel/propagation
go.opentelemetry.io/otel/semconv/internal
go.opentelemetry.io/otel/semconv/v1.24.0
go.opentelemetry.io/otel/semconv/v1.4.0
# go.opentelemetry.io/otel/metric v1.26.0
## explicit; go 1.21
go.opentelemetry.io/otel/metric
go.opentelemetry.io/otel/metric/embedded
go.opentelemetry.io/otel/metric/noop
# go.opentelemetry.io/otel/sdk v1.24.0
## explicit; go 1.20
go.opentelemetry.io/otel/sdk
go.opentelemetry.io/otel/sdk/instrumentation
go.opentelemetry.io/otel/sdk/resource
# go.opentelemetry.io/otel/sdk/metric v1.24.0
## explicit; go 1.20
go.opentelemetry.io/otel/sdk/metric
go.opentelemetry.io/otel/sdk/metric/internal
go.opentelemetry.io/otel/sdk/metric/internal/aggregate
go.opentelemetry.io/otel/sdk/metric/internal/exemplar
go.opentelemetry.io/otel/sdk/metric/internal/x
go.opentelemetry.io/otel/sdk/metric/metricdata
# go.opentelemetry.io/otel/trace v1.26.0
## explicit; go 1.21
go.opentelemetry.io/otel/trace
go.opentelemetry.io/otel/trace/embedded
# go.starlark.net v0.0.0-20230302034142-4b1e35fe2254
## explicit; go 1.16
go.starlark.net/internal/compile
go.starlark.net/internal/spell
go.starlark.net/lib/math
go.starlark.net/resolve
go.starlark.net/starlark
go.starlark.net/starlarkstruct
go.starlark.net/syntax
# go.temporal.io/api v1.46.0
## explicit; go 1.21
go.temporal.io/api/activity/v1
go.temporal.io/api/batch/v1
go.temporal.io/api/command/v1
go.temporal.io/api/common/v1
go.temporal.io/api/deployment/v1
go.temporal.io/api/enums/v1
go.temporal.io/api/errordetails/v1
go.temporal.io/api/export/v1
go.temporal.io/api/failure/v1
go.temporal.io/api/filter/v1
go.temporal.io/api/history/v1
go.temporal.io/api/internal/protojson
go.temporal.io/api/internal/protojson/errors
go.temporal.io/api/internal/protojson/genid
go.temporal.io/api/internal/protojson/json
go.temporal.io/api/internal/protojson/order
go.temporal.io/api/internal/protojson/set
go.temporal.io/api/internal/protojson/strs
go.temporal.io/api/internal/strcase
go.temporal.io/api/namespace/v1
go.temporal.io/api/nexus/v1
go.temporal.io/api/operatorservice/v1
go.temporal.io/api/protocol/v1
go.temporal.io/api/proxy
go.temporal.io/api/query/v1
go.temporal.io/api/replication/v1
go.temporal.io/api/schedule/v1
go.temporal.io/api/sdk/v1
go.temporal.io/api/serviceerror
go.temporal.io/api/taskqueue/v1
go.temporal.io/api/temporalproto
go.temporal.io/api/update/v1
go.temporal.io/api/version/v1
go.temporal.io/api/workflow/v1
go.temporal.io/api/workflowservice/v1
go.temporal.io/api/workflowservicemock/v1
# go.temporal.io/sdk v1.34.0
## explicit; go 1.23.0
go.temporal.io/sdk/activity
go.temporal.io/sdk/client
go.temporal.io/sdk/converter
go.temporal.io/sdk/internal
go.temporal.io/sdk/internal/common/backoff
go.temporal.io/sdk/internal/common/cache
go.temporal.io/sdk/internal/common/metrics
go.temporal.io/sdk/internal/common/retry
go.temporal.io/sdk/internal/common/serializer
go.temporal.io/sdk/internal/common/util
go.temporal.io/sdk/internal/log
go.temporal.io/sdk/internal/protocol
go.temporal.io/sdk/log
go.temporal.io/sdk/temporal
go.temporal.io/sdk/worker
go.temporal.io/sdk/workflow
# go.uber.org/multierr v1.11.0
## explicit; go 1.19
go.uber.org/multierr
# go.uber.org/zap v1.27.0
## explicit; go 1.19
go.uber.org/zap
go.uber.org/zap/buffer
go.uber.org/zap/internal
go.uber.org/zap/internal/bufferpool
go.uber.org/zap/internal/color
go.uber.org/zap/internal/exit
go.uber.org/zap/internal/pool
go.uber.org/zap/internal/stacktrace
go.uber.org/zap/zapcore
# golang.org/x/arch v0.15.0
## explicit; go 1.23.0
golang.org/x/arch/x86/x86asm
# golang.org/x/crypto v0.38.0
## explicit; go 1.23.0
golang.org/x/crypto/argon2
golang.org/x/crypto/bcrypt
golang.org/x/crypto/blake2b
golang.org/x/crypto/blowfish
golang.org/x/crypto/ocsp
golang.org/x/crypto/pbkdf2
golang.org/x/crypto/scrypt
golang.org/x/crypto/sha3
# golang.org/x/exp v0.0.0-20230713183714-613f0c0eb8a1
## explicit; go 1.20
golang.org/x/exp/constraints
golang.org/x/exp/maps
golang.org/x/exp/slices
# golang.org/x/image v0.25.0
## explicit; go 1.23.0
golang.org/x/image/bmp
golang.org/x/image/ccitt
golang.org/x/image/draw
golang.org/x/image/font
golang.org/x/image/math/f64
golang.org/x/image/math/fixed
golang.org/x/image/tiff
golang.org/x/image/tiff/lzw
# golang.org/x/net v0.40.0
## explicit; go 1.23.0
golang.org/x/net/html
golang.org/x/net/html/atom
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/h2c
golang.org/x/net/http2/hpack
golang.org/x/net/idna
golang.org/x/net/internal/httpcommon
golang.org/x/net/internal/timeseries
golang.org/x/net/publicsuffix
golang.org/x/net/trace
# golang.org/x/sync v0.14.0
## explicit; go 1.23.0
golang.org/x/sync/errgroup
golang.org/x/sync/semaphore
# golang.org/x/sys v0.33.0
## explicit; go 1.23.0
golang.org/x/sys/cpu
golang.org/x/sys/unix
golang.org/x/sys/windows
golang.org/x/sys/windows/registry
# golang.org/x/text v0.25.0
## explicit; go 1.23.0
golang.org/x/text/cases
golang.org/x/text/encoding
golang.org/x/text/encoding/charmap
golang.org/x/text/encoding/internal
golang.org/x/text/encoding/internal/identifier
golang.org/x/text/encoding/japanese
golang.org/x/text/encoding/simplifiedchinese
golang.org/x/text/encoding/traditionalchinese
golang.org/x/text/encoding/unicode
golang.org/x/text/encoding/unicode/utf32
golang.org/x/text/internal
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/tag
golang.org/x/text/internal/utf8internal
golang.org/x/text/language
golang.org/x/text/runes
golang.org/x/text/secure/bidirule
golang.org/x/text/secure/precis
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
golang.org/x/text/width
# golang.org/x/time v0.11.0
## explicit; go 1.23.0
golang.org/x/time/rate
# golang.org/x/xerrors v0.0.0-20240903120638-7835f813f4da
## explicit; go 1.18
golang.org/x/xerrors
golang.org/x/xerrors/internal
# google.golang.org/genproto/googleapis/api v0.0.0-20240827150818-7e3bb234dfed
## explicit; go 1.21
google.golang.org/genproto/googleapis/api
google.golang.org/genproto/googleapis/api/annotations
google.golang.org/genproto/googleapis/api/httpbody
# google.golang.org/genproto/googleapis/rpc v0.0.0-20240827150818-7e3bb234dfed
## explicit; go 1.21
google.golang.org/genproto/googleapis/rpc/errdetails
google.golang.org/genproto/googleapis/rpc/status
# google.golang.org/grpc v1.66.0
## explicit; go 1.21
google.golang.org/grpc
google.golang.org/grpc/admin
google.golang.org/grpc/attributes
google.golang.org/grpc/backoff
google.golang.org/grpc/balancer
google.golang.org/grpc/balancer/base
google.golang.org/grpc/balancer/grpclb/state
google.golang.org/grpc/balancer/pickfirst
google.golang.org/grpc/balancer/roundrobin
google.golang.org/grpc/binarylog/grpc_binarylog_v1
google.golang.org/grpc/channelz
google.golang.org/grpc/channelz/grpc_channelz_v1
google.golang.org/grpc/channelz/internal/protoconv
google.golang.org/grpc/channelz/service
google.golang.org/grpc/codes
google.golang.org/grpc/connectivity
google.golang.org/grpc/credentials
google.golang.org/grpc/credentials/insecure
google.golang.org/grpc/encoding
google.golang.org/grpc/encoding/proto
google.golang.org/grpc/experimental/stats
google.golang.org/grpc/grpclog
google.golang.org/grpc/grpclog/internal
google.golang.org/grpc/health
google.golang.org/grpc/health/grpc_health_v1
google.golang.org/grpc/internal
google.golang.org/grpc/internal/admin
google.golang.org/grpc/internal/backoff
google.golang.org/grpc/internal/balancer/gracefulswitch
google.golang.org/grpc/internal/balancerload
google.golang.org/grpc/internal/binarylog
google.golang.org/grpc/internal/buffer
google.golang.org/grpc/internal/channelz
google.golang.org/grpc/internal/credentials
google.golang.org/grpc/internal/envconfig
google.golang.org/grpc/internal/grpclog
google.golang.org/grpc/internal/grpcsync
google.golang.org/grpc/internal/grpcutil
google.golang.org/grpc/internal/idle
google.golang.org/grpc/internal/metadata
google.golang.org/grpc/internal/pretty
google.golang.org/grpc/internal/resolver
google.golang.org/grpc/internal/resolver/dns
google.golang.org/grpc/internal/resolver/dns/internal
google.golang.org/grpc/internal/resolver/passthrough
google.golang.org/grpc/internal/resolver/unix
google.golang.org/grpc/internal/serviceconfig
google.golang.org/grpc/internal/stats
google.golang.org/grpc/internal/status
google.golang.org/grpc/internal/syscall
google.golang.org/grpc/internal/transport
google.golang.org/grpc/internal/transport/networktype
google.golang.org/grpc/keepalive
google.golang.org/grpc/mem
google.golang.org/grpc/metadata
google.golang.org/grpc/peer
google.golang.org/grpc/reflection
google.golang.org/grpc/reflection/grpc_reflection_v1
google.golang.org/grpc/reflection/grpc_reflection_v1alpha
google.golang.org/grpc/reflection/internal
google.golang.org/grpc/resolver
google.golang.org/grpc/resolver/dns
google.golang.org/grpc/serviceconfig
google.golang.org/grpc/stats
google.golang.org/grpc/status
google.golang.org/grpc/tap
# google.golang.org/protobuf v1.36.6
## explicit; go 1.22
google.golang.org/protobuf/encoding/protodelim
google.golang.org/protobuf/encoding/protojson
google.golang.org/protobuf/encoding/prototext
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/descfmt
google.golang.org/protobuf/internal/descopts
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/editiondefaults
google.golang.org/protobuf/internal/editionssupport
google.golang.org/protobuf/internal/encoding/defval
google.golang.org/protobuf/internal/encoding/json
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/encoding/tag
google.golang.org/protobuf/internal/encoding/text
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/filedesc
google.golang.org/protobuf/internal/filetype
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/impl
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/protolazy
google.golang.org/protobuf/internal/set
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/internal/version
google.golang.org/protobuf/proto
google.golang.org/protobuf/protoadapt
google.golang.org/protobuf/reflect/protodesc
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
google.golang.org/protobuf/runtime/protoimpl
google.golang.org/protobuf/types/descriptorpb
google.golang.org/protobuf/types/gofeaturespb
google.golang.org/protobuf/types/known/anypb
google.golang.org/protobuf/types/known/durationpb
google.golang.org/protobuf/types/known/emptypb
google.golang.org/protobuf/types/known/fieldmaskpb
google.golang.org/protobuf/types/known/structpb
google.golang.org/protobuf/types/known/timestamppb
google.golang.org/protobuf/types/known/wrapperspb
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# gorm.io/driver/postgres v1.6.0
## explicit; go 1.20
gorm.io/driver/postgres
# gorm.io/gorm v1.30.0
## explicit; go 1.18
gorm.io/gorm
gorm.io/gorm/callbacks
gorm.io/gorm/clause
gorm.io/gorm/internal/lru
gorm.io/gorm/internal/stmt_store
gorm.io/gorm/logger
gorm.io/gorm/migrator
gorm.io/gorm/schema
gorm.io/gorm/utils
