package email

import (
	"context"
	"log"
	"os"

	"gopkg.in/yaml.v3"
)

type Config struct {
	StorageBucket string `yaml:"storage_bucket"`
	Extraction    struct {
		Format string `yaml:"format"`
	} `yaml:"extraction"`
}

type Processor struct {
	config *Config
	// storage   *storage.MinioService
	// extractor extraction.XLSXExtractor
}

func NewProcessor(configPath string) (*Processor, error) {
	configData, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	var config Config
	if err := yaml.Unmarshal(configData, &config); err != nil {
		return nil, err
	}

	return &Processor{
		config: &config,
	}, nil
}

func (p *Processor) ProcessEmail(ctx context.Context, emailPath string) error {
	data, err := os.ReadFile(emailPath)
	if err != nil {
		log.Printf("Error reading email: %v", err)
		return err
	}

	// TODO: Implement extraction and storage when needed
	log.Printf("Processing email from: %s", emailPath)
	log.Printf("Email data size: %d bytes", len(data))

	// Placeholder for future implementation
	switch p.config.Extraction.Format {
	case "xlsx":
		log.Printf("XLSX extraction not implemented yet")
	default:
		log.Printf("Unsupported extraction format: %s", p.config.Extraction.Format)
	}

	return nil
}
