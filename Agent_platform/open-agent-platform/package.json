{"name": "open-agent-platform", "author": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "workspaces": ["apps/*"], "scripts": {"dev": "turbo dev", "build": "turbo build", "turbo:command": "turbo", "format": "turbo format", "lint": "turbo lint", "lint:fix": "turbo lint:fix"}, "devDependencies": {"turbo": "^2.5.0", "typescript": "^5"}, "packageManager": "yarn@3.5.1", "dependencies": {"react-hook-form": "^7.56.3"}}