---
title: 'Agents Setup'
description: 'Deploy and configure your agents for Open Agent Platform'
---

The first step in setting up Open Agent Platform is to deploy and configure your agents. To help with this, we're releasing two pre-built agents, customized specifically for Open Agent Platform:

- [Tools Agent](https://github.com/langchain-ai/oap-langgraph-tools-agent)
- [Supervisor Agent](https://github.com/langchain-ai/oap-agent-supervisor)

## Deploying Agents

To use these agents in your instance, you should:

1. Clone the repositories
2. Follow the instructions in the READMEs
3. Deploy the agents to LangGraph Platform

## Configuration

Once deployed, you can connect them to your instance of Open Agent Platform by setting the configuration environment variables. The configuration object is as follows:

```json
{
  "id": "The project ID of the deployment. For locally running LangGraph servers, this can be any UUID.",
  "tenantId": "The tenant ID of your LangSmith account. For locally running LangGraph servers, this can be any UUID.",
  "deploymentUrl": "The API URL to your deployment",
  "name": "A custom name for your deployment",
  "isDefault": "Whether this deployment is the default deployment. Should only be set to true for one deployment.",
  "defaultGraphId": "The graph ID of the default graph for the entire OAP instance. We recommend this is set to the graph ID of a graph which supports RAG & MCP tools. This must be set in the same deployment which isDefault is set to true on. Optional, but required in at least one deployment.",
}
```

## Finding Project & Tenant IDs

To easily find your project & tenant IDs, you can make a GET request to the `/info` endpoint of your deployment URL. Then, copy the `project_id` value into `id`, and `tenant_id` into `tenantId`.

## Setting Environment Variables

After constructing the JSON objects with these values for each of the deployments you want to include in your Open Agent Platform instance, you should stringify them into a single, flat array, then set them under the `NEXT_PUBLIC_DEPLOYMENTS` environment variable inside the `apps/web/` directory.

The following is an example of what this variable would look like for a single, local deployment:

```bash
NEXT_PUBLIC_DEPLOYMENTS=[{"id":"bf63dc89-1de7-4a65-8336-af9ecda479d6","deploymentUrl":"http://localhost:2024","tenantId":"42d732b3-1324-4226-9fe9-513044dceb58","name":"Local deployment","isDefault":true,"defaultGraphId":"agent"}]
```
