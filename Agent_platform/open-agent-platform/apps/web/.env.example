# The base API URL for the platform.
# Defaults to `http://localhost:3000/api` for development
NEXT_PUBLIC_BASE_API_URL="http://localhost:3000/api"

# LangSmith API key required for some admin tasks.
LANGSMITH_API_KEY="lsv2_..."
# Whether or not to always use <PERSON><PERSON><PERSON> auth (API key). If true, you will
# not get user scoped auth by default
NEXT_PUBLIC_USE_LANGSMITH_AUTH="false"

# The deployments to make available in the UI
NEXT_PUBLIC_DEPLOYMENTS="[]"

# The RAG API URL for the platform.
NEXT_PUBLIC_RAG_API_URL="http://localhost:8080"

# The base URL to the MCP server. Do not include the `/mcp` at the end.
NEXT_PUBLIC_MCP_SERVER_URL=""
# Whether or not the MCP server requires authentication.
# If true, all requests to the MCP server will go through a proxy
# route first.
NEXT_PUBLIC_MCP_AUTH_REQUIRED="true"

# Supabase Authentication
NEXT_PUBLIC_SUPABASE_ANON_KEY=""
NEXT_PUBLIC_SUPABASE_URL=""

# Disable showing Google Auth in the UI
# Defaults to false.
NEXT_PUBLIC_GOOGLE_AUTH_DISABLED="false"