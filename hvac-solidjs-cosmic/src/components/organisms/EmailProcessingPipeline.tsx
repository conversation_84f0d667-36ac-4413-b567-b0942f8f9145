import { type Component, createSignal, createEffect, onMount, Show, For } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import {
  Mail,
  Brain,
  Zap,
  CheckCircle,
  Clock,
  AlertTriangle,
  ArrowRight,
  Activity,
  Sparkles,
  Target,
  MessageSquare,
  Users,
  Wrench,
  FileText,
  Database,
  Wifi
} from 'lucide-solid'

// 🔄 Pipeline Stage Types
interface PipelineStage {
  id: string
  name: string
  description: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  duration?: number
  icon: any
  details?: string[]
}

interface EmailPipelineData {
  emailId: string
  subject: string
  from: string
  timestamp: string
  stages: PipelineStage[]
  currentStage: number
  totalProcessingTime: number
  a2aSkillsTriggered: string[]
}

export const EmailProcessingPipeline: Component = () => {
  // 🌟 Cosmic State Management
  const [isLoaded, setIsLoaded] = createSignal(false)
  const [activePipelines, setActivePipelines] = createSignal<EmailPipelineData[]>([])
  const [selectedPipeline, setSelectedPipeline] = createSignal<EmailPipelineData | null>(null)
  const [isRealTimeEnabled, setIsRealTimeEnabled] = createSignal(true)

  // 🚀 Mock Pipeline Data
  const mockPipelineData = (): EmailPipelineData[] => [
    {
      emailId: 'email_001',
      subject: 'Urgent AC Repair Request',
      from: '<EMAIL>',
      timestamp: new Date().toISOString(),
      currentStage: 3,
      totalProcessingTime: 2.8,
      a2aSkillsTriggered: ['hvac_diagnostics', 'customer_inquiry', 'scheduling_assistance'],
      stages: [
        {
          id: 'receive',
          name: 'Email Received',
          description: 'Email retrieved from mailbox',
          status: 'completed',
          duration: 0.1,
          icon: Mail,
          details: ['IMAP connection established', 'Email parsed successfully']
        },
        {
          id: 'parse',
          name: 'Content Parsing',
          description: 'Extract content and attachments',
          status: 'completed',
          duration: 0.3,
          icon: FileText,
          details: ['Text content extracted', 'No attachments found', 'Encoding: UTF-8']
        },
        {
          id: 'ai_analysis',
          name: 'AI Analysis',
          description: 'Bielik V3 sentiment & intent analysis',
          status: 'completed',
          duration: 1.2,
          icon: Brain,
          details: ['Sentiment: Negative', 'Intent: Service Request', 'Priority: High', 'Confidence: 94%']
        },
        {
          id: 'a2a_processing',
          name: 'A2A Skills',
          description: 'Agent-to-Agent skill execution',
          status: 'processing',
          duration: 0.8,
          icon: Zap,
          details: ['HVAC Diagnostics: Running', 'Customer Inquiry: Completed', 'Scheduling: Pending']
        },
        {
          id: 'database_update',
          name: 'Database Update',
          description: 'Store results in PostgreSQL',
          status: 'pending',
          icon: Database,
          details: ['Waiting for A2A completion']
        },
        {
          id: 'notification',
          name: 'Notifications',
          description: 'Send alerts and updates',
          status: 'pending',
          icon: MessageSquare,
          details: ['Customer notification pending', 'Technician assignment pending']
        }
      ]
    },
    {
      emailId: 'email_002',
      subject: 'Quote Request for New Installation',
      from: '<EMAIL>',
      timestamp: new Date(Date.now() - 300000).toISOString(),
      currentStage: 5,
      totalProcessingTime: 3.2,
      a2aSkillsTriggered: ['quote_generation', 'customer_inquiry'],
      stages: [
        {
          id: 'receive',
          name: 'Email Received',
          description: 'Email retrieved from mailbox',
          status: 'completed',
          duration: 0.1,
          icon: Mail
        },
        {
          id: 'parse',
          name: 'Content Parsing',
          description: 'Extract content and attachments',
          status: 'completed',
          duration: 0.2,
          icon: FileText
        },
        {
          id: 'ai_analysis',
          name: 'AI Analysis',
          description: 'Bielik V3 sentiment & intent analysis',
          status: 'completed',
          duration: 1.1,
          icon: Brain
        },
        {
          id: 'a2a_processing',
          name: 'A2A Skills',
          description: 'Agent-to-Agent skill execution',
          status: 'completed',
          duration: 1.5,
          icon: Zap
        },
        {
          id: 'database_update',
          name: 'Database Update',
          description: 'Store results in PostgreSQL',
          status: 'completed',
          duration: 0.2,
          icon: Database
        },
        {
          id: 'notification',
          name: 'Notifications',
          description: 'Send alerts and updates',
          status: 'processing',
          duration: 0.1,
          icon: MessageSquare
        }
      ]
    }
  ]

  // 🎭 Load Pipeline Data
  onMount(() => {
    setTimeout(() => setIsLoaded(true), 300)
    setActivePipelines(mockPipelineData())
    
    // Simulate real-time updates
    if (isRealTimeEnabled()) {
      const interval = setInterval(() => {
        setActivePipelines(prev => prev.map(pipeline => {
          if (pipeline.currentStage < pipeline.stages.length - 1) {
            const newPipeline = { ...pipeline }
            // Advance processing occasionally
            if (Math.random() > 0.7) {
              newPipeline.currentStage += 1
              newPipeline.stages[newPipeline.currentStage].status = 'processing'
              if (newPipeline.currentStage > 0) {
                newPipeline.stages[newPipeline.currentStage - 1].status = 'completed'
              }
            }
            return newPipeline
          }
          return pipeline
        }))
      }, 3000)
      
      return () => clearInterval(interval)
    }
  })

  // 🎨 Get Stage Status Color
  const getStageColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400'
      case 'processing': return 'text-cosmic-400'
      case 'error': return 'text-red-400'
      default: return 'text-white/40'
    }
  }

  // 🎨 Get Stage Background
  const getStageBackground = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500/20 border-green-400/30'
      case 'processing': return 'bg-cosmic-500/20 border-cosmic-400/30'
      case 'error': return 'bg-red-500/20 border-red-400/30'
      default: return 'bg-white/5 border-white/10'
    }
  }

  return (
    <div class="space-y-golden-lg">
      {/* Header */}
      <div
        class={`flex flex-col lg:flex-row lg:items-center lg:justify-between transition-all duration-1000 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <div>
          <h2 class="text-2xl font-bold text-white mb-golden-sm flex items-center">
            <Activity size={28} class="mr-golden-sm text-cosmic-400" />
            Email Processing Pipeline
          </h2>
          <p class="text-white/70">
            Real-time visualization of email processing stages
          </p>
        </div>
        
        <div class="flex gap-golden-sm mt-golden-md lg:mt-0">
          <GoldenButton
            variant={isRealTimeEnabled() ? "cosmic" : "golden"}
            size="md"
            glow
            physics
            onClick={() => setIsRealTimeEnabled(!isRealTimeEnabled())}
          >
            <Wifi size={16} class="mr-golden-xs" />
            {isRealTimeEnabled() ? 'Live' : 'Paused'}
          </GoldenButton>
        </div>
      </div>

      {/* Active Pipelines */}
      <div
        class={`transition-all duration-1000 delay-200 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <div class="space-y-golden-md">
          <For each={activePipelines()}>
            {(pipeline) => (
              <CosmicCard variant="glass" glow hover3d>
                <div class="p-golden-lg">
                  {/* Pipeline Header */}
                  <div class="flex items-start justify-between mb-golden-md">
                    <div class="flex-1">
                      <h3 class="text-lg font-semibold text-white mb-golden-xs">
                        {pipeline.subject}
                      </h3>
                      <p class="text-white/70 text-sm mb-golden-xs">
                        From: {pipeline.from}
                      </p>
                      <div class="flex items-center gap-golden-md text-sm text-white/60">
                        <span>Processing Time: {pipeline.totalProcessingTime}s</span>
                        <span>Stage: {pipeline.currentStage + 1}/{pipeline.stages.length}</span>
                        <span>A2A Skills: {pipeline.a2aSkillsTriggered.length}</span>
                      </div>
                    </div>
                    
                    <GoldenButton
                      variant="divine"
                      size="sm"
                      glow
                      onClick={() => setSelectedPipeline(pipeline)}
                    >
                      <Target size={16} class="mr-golden-xs" />
                      Details
                    </GoldenButton>
                  </div>

                  {/* Pipeline Stages */}
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-golden-sm">
                    <For each={pipeline.stages}>
                      {(stage, index) => {
                        const Icon = stage.icon
                        const isActive = index() === pipeline.currentStage
                        const isCompleted = index() < pipeline.currentStage
                        const status = isCompleted ? 'completed' : isActive ? 'processing' : 'pending'
                        
                        return (
                          <div class={`relative p-golden-sm rounded-lg border transition-all duration-300 ${getStageBackground(status)}`}>
                            {/* Stage Icon */}
                            <div class={`flex items-center justify-center w-8 h-8 rounded-full mb-golden-xs ${
                              status === 'completed' ? 'bg-green-500/30' :
                              status === 'processing' ? 'bg-cosmic-500/30' :
                              'bg-white/10'
                            }`}>
                              <Icon size={16} class={getStageColor(status)} />
                            </div>
                            
                            {/* Stage Info */}
                            <h4 class={`text-sm font-medium mb-golden-xs ${getStageColor(status)}`}>
                              {stage.name}
                            </h4>
                            <p class="text-xs text-white/60 leading-tight">
                              {stage.description}
                            </p>
                            
                            {/* Duration */}
                            {stage.duration && (
                              <div class="mt-golden-xs text-xs text-white/50">
                                {stage.duration}s
                              </div>
                            )}
                            
                            {/* Processing Animation */}
                            {status === 'processing' && (
                              <div class="absolute inset-0 rounded-lg bg-cosmic-400/10 animate-pulse" />
                            )}
                            
                            {/* Arrow to Next Stage */}
                            {index() < pipeline.stages.length - 1 && (
                              <div class="hidden xl:block absolute -right-2 top-1/2 transform -translate-y-1/2 z-10">
                                <ArrowRight size={16} class="text-white/30" />
                              </div>
                            )}
                          </div>
                        )
                      }}
                    </For>
                  </div>
                </div>
              </CosmicCard>
            )}
          </For>
        </div>
      </div>
    </div>
  )
}
