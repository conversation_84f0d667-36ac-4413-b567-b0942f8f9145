// 🧠 Email Intelligence API Integration
// Connects to GoBackend-Kratos email processing services

import { createQuery, createMutation } from '@tanstack/solid-query'

// 📧 Email Intelligence Types
export interface EmailStats {
  totalEmails: number
  todayEmails: number
  hvacEmails: number
  highPriorityEmails: number
  attachmentsCount: number
  processingRate: number
  aiAccuracy: number
  responseTime: number
  sentimentBreakdown: {
    positive: number
    negative: number
    neutral: number
  }
  categoryBreakdown: {
    hvacService: number
    support: number
    sales: number
    general: number
  }
}

export interface EmailAnalysis {
  id: string
  subject: string
  from: string
  to: string
  category: string
  sentiment: 'positive' | 'negative' | 'neutral'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  confidence: number
  timestamp: string
  aiInsights: string[]
  a2aProcessed: boolean
  bodyAnalysis?: {
    keyPhrases: string[]
    entities: string[]
    summary: string
  }
  hvacRelevance?: {
    isHVACRelated: boolean
    equipmentMentioned: string[]
    serviceMentioned: string[]
  }
}

export interface A2AStatus {
  isConnected: boolean
  activeSkills: number
  processedToday: number
  averageResponseTime: number
  successRate: number
  skillsStatus: {
    [skillName: string]: {
      status: 'active' | 'inactive' | 'error'
      lastExecution: string
      successCount: number
      errorCount: number
    }
  }
}

export interface EmailProcessingResult {
  emailId: string
  processingTime: number
  stages: {
    name: string
    status: 'completed' | 'processing' | 'error'
    duration: number
    details: string[]
  }[]
  a2aSkillsTriggered: string[]
  errors?: string[]
}

// 🌐 API Base Configuration
const API_BASE = 'http://localhost:8080/api/v1'

// 📊 Email Intelligence API Functions
export const emailIntelligenceAPI = {
  // Get dashboard statistics
  async getDashboardStats(): Promise<EmailStats> {
    const response = await fetch(`${API_BASE}/email-analysis/dashboard/stats`)
    if (!response.ok) {
      throw new Error('Failed to fetch dashboard stats')
    }
    const result = await response.json()
    return result.data // Extract data from UnifiedCRMResponse
  },

  // Get recent email analysis
  async getRecentAnalysis(limit: number = 10): Promise<EmailAnalysis[]> {
    const response = await fetch(`${API_BASE}/email-analysis/recent?limit=${limit}`)
    if (!response.ok) {
      throw new Error('Failed to fetch recent analysis')
    }
    const result = await response.json()
    return result.data // Extract data from UnifiedCRMResponse
  },

  // Get A2A status
  async getA2AStatus(): Promise<A2AStatus> {
    const response = await fetch(`${API_BASE}/a2a/status`)
    if (!response.ok) {
      throw new Error('Failed to fetch A2A status')
    }
    const result = await response.json()
    return result.data // Extract data from UnifiedCRMResponse
  },

  // Search emails
  async searchEmails(query: string, filters?: {
    category?: string
    sentiment?: string
    priority?: string
    dateFrom?: string
    dateTo?: string
  }): Promise<EmailAnalysis[]> {
    const searchBody = {
      query,
      filters: filters || {}
    }

    const response = await fetch(`${API_BASE}/email-analysis/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(searchBody),
    })
    if (!response.ok) {
      throw new Error('Failed to search emails')
    }
    const result = await response.json()
    return result.data // Extract data from UnifiedCRMResponse
  },

  // Get email details
  async getEmailDetails(emailId: string): Promise<EmailAnalysis> {
    const response = await fetch(`${API_BASE}/email-analysis/emails/${emailId}?id=${emailId}`)
    if (!response.ok) {
      throw new Error('Failed to fetch email details')
    }
    const result = await response.json()
    return result.data // Extract data from UnifiedCRMResponse
  },

  // Trigger manual email analysis
  async analyzeEmail(emailData: {
    subject: string
    from: string
    to: string
    body: string
  }): Promise<EmailProcessingResult> {
    const response = await fetch(`${API_BASE}/email-analysis/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailData),
    })
    if (!response.ok) {
      throw new Error('Failed to analyze email')
    }
    const result = await response.json()
    return result.data // Extract data from UnifiedCRMResponse
  },

  // Get processing pipeline status
  async getProcessingPipelines(): Promise<EmailProcessingResult[]> {
    const response = await fetch(`${API_BASE}/email-analysis/pipelines`)
    if (!response.ok) {
      throw new Error('Failed to fetch processing pipelines')
    }
    const result = await response.json()
    return result.data // Extract data from UnifiedCRMResponse
  },

  // Trigger A2A skill manually
  async triggerA2ASkill(skillName: string, emailId: string): Promise<any> {
    const response = await fetch(`${API_BASE}/a2a/skills/${skillName}/execute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ emailId }),
    })
    if (!response.ok) {
      throw new Error(`Failed to trigger A2A skill: ${skillName}`)
    }
    const result = await response.json()
    return result.data // Extract data from UnifiedCRMResponse
  }
}

// 🎣 SolidJS Query Hooks
export const useEmailStats = () => {
  return createQuery(() => ({
    queryKey: ['emailStats'],
    queryFn: emailIntelligenceAPI.getDashboardStats,
    refetchInterval: 30000, // Refresh every 30 seconds
  }))
}

export const useRecentAnalysis = (limit: number = 10) => {
  return createQuery(() => ({
    queryKey: ['recentAnalysis', limit],
    queryFn: () => emailIntelligenceAPI.getRecentAnalysis(limit),
    refetchInterval: 15000, // Refresh every 15 seconds
  }))
}

export const useA2AStatus = () => {
  return createQuery(() => ({
    queryKey: ['a2aStatus'],
    queryFn: emailIntelligenceAPI.getA2AStatus,
    refetchInterval: 10000, // Refresh every 10 seconds
  }))
}

export const useProcessingPipelines = () => {
  return createQuery(() => ({
    queryKey: ['processingPipelines'],
    queryFn: emailIntelligenceAPI.getProcessingPipelines,
    refetchInterval: 5000, // Refresh every 5 seconds for real-time updates
  }))
}

export const useEmailSearch = () => {
  return createMutation(() => ({
    mutationFn: ({ query, filters }: { 
      query: string
      filters?: {
        category?: string
        sentiment?: string
        priority?: string
        dateFrom?: string
        dateTo?: string
      }
    }) => emailIntelligenceAPI.searchEmails(query, filters),
  }))
}

export const useEmailAnalysis = () => {
  return createMutation(() => ({
    mutationFn: (emailData: {
      subject: string
      from: string
      to: string
      body: string
    }) => emailIntelligenceAPI.analyzeEmail(emailData),
  }))
}

export const useA2ASkillTrigger = () => {
  return createMutation(() => ({
    mutationFn: ({ skillName, emailId }: { skillName: string; emailId: string }) =>
      emailIntelligenceAPI.triggerA2ASkill(skillName, emailId),
  }))
}

// 🔄 WebSocket for Real-time Updates
export class EmailIntelligenceWebSocket {
  private ws: WebSocket | null = null
  private listeners: Map<string, Function[]> = new Map()

  connect() {
    if (this.ws?.readyState === WebSocket.OPEN) return

    this.ws = new WebSocket('ws://localhost:8080/ws/email-intelligence')
    
    this.ws.onopen = () => {
      console.log('📡 Email Intelligence WebSocket connected')
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        const { type, payload } = data
        
        const typeListeners = this.listeners.get(type) || []
        typeListeners.forEach(listener => listener(payload))
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error)
      }
    }

    this.ws.onclose = () => {
      console.log('📡 Email Intelligence WebSocket disconnected')
      // Attempt to reconnect after 5 seconds
      setTimeout(() => this.connect(), 5000)
    }

    this.ws.onerror = (error) => {
      console.error('📡 Email Intelligence WebSocket error:', error)
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  subscribe(type: string, listener: Function) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, [])
    }
    this.listeners.get(type)!.push(listener)

    // Return unsubscribe function
    return () => {
      const typeListeners = this.listeners.get(type) || []
      const index = typeListeners.indexOf(listener)
      if (index > -1) {
        typeListeners.splice(index, 1)
      }
    }
  }

  send(type: string, payload: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({ type, payload }))
    }
  }
}

// 🌟 Global WebSocket Instance
export const emailIntelligenceWS = new EmailIntelligenceWebSocket()
